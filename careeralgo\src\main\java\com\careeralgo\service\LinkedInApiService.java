package com.careeralgo.service;

import com.careeralgo.model.User;
import com.careeralgo.model.ParsedContent;
import com.careeralgo.repository.UserRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Service for LinkedIn API integration
 */
@Service
public class LinkedInApiService {

    private static final Logger logger = LoggerFactory.getLogger(LinkedInApiService.class);

    @Value("${careeralgo.external-apis.linkedin.client-id}")
    private String clientId;

    @Value("${careeralgo.external-apis.linkedin.client-secret}")
    private String clientSecret;

    @Value("${careeralgo.external-apis.linkedin.redirect-uri}")
    private String redirectUri;

    @Value("${careeralgo.external-apis.linkedin.base-url:https://api.linkedin.com/v2}")
    private String baseUrl;

    @Value("${careeralgo.external-apis.linkedin.enabled:false}")
    private boolean enabled;

    @Autowired
    private UserRepository userRepository;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Generate LinkedIn OAuth authorization URL
     */
    public String generateAuthorizationUrl(String state) {
        if (!enabled) {
            throw new UnsupportedOperationException("LinkedIn integration is disabled");
        }

        String scope = "r_liteprofile r_emailaddress w_member_social";
        
        return "https://www.linkedin.com/oauth/v2/authorization" +
                "?response_type=code" +
                "&client_id=" + clientId +
                "&redirect_uri=" + redirectUri +
                "&state=" + state +
                "&scope=" + scope;
    }

    /**
     * Exchange authorization code for access token
     */
    public LinkedInTokenResponse exchangeCodeForToken(String authorizationCode) {
        if (!enabled) {
            throw new UnsupportedOperationException("LinkedIn integration is disabled");
        }

        try {
            String tokenUrl = "https://www.linkedin.com/oauth/v2/accessToken";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            String requestBody = "grant_type=authorization_code" +
                    "&code=" + authorizationCode +
                    "&redirect_uri=" + redirectUri +
                    "&client_id=" + clientId +
                    "&client_secret=" + clientSecret;
            
            HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(tokenUrl, request, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode tokenData = objectMapper.readTree(response.getBody());
                
                LinkedInTokenResponse tokenResponse = new LinkedInTokenResponse();
                tokenResponse.setAccessToken(tokenData.path("access_token").asText());
                tokenResponse.setTokenType(tokenData.path("token_type").asText());
                tokenResponse.setExpiresIn(tokenData.path("expires_in").asInt());
                tokenResponse.setScope(tokenData.path("scope").asText());
                
                return tokenResponse;
            } else {
                throw new RuntimeException("Failed to exchange code for token: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            logger.error("Error exchanging LinkedIn authorization code for token", e);
            throw new RuntimeException("LinkedIn token exchange failed", e);
        }
    }

    /**
     * Get LinkedIn profile information
     */
    public LinkedInProfile getProfile(String accessToken) {
        if (!enabled) {
            throw new UnsupportedOperationException("LinkedIn integration is disabled");
        }

        try {
            // Get basic profile
            String profileUrl = baseUrl + "/people/~:(id,firstName,lastName,headline,summary,industry,location,pictureUrl,publicProfileUrl)";
            JsonNode profileData = makeLinkedInApiCall(profileUrl, accessToken);
            
            // Get email address
            String emailUrl = baseUrl + "/emailAddress?q=members&projection=(elements*(handle~))";
            JsonNode emailData = makeLinkedInApiCall(emailUrl, accessToken);
            
            return parseLinkedInProfile(profileData, emailData);
            
        } catch (Exception e) {
            logger.error("Error fetching LinkedIn profile", e);
            throw new RuntimeException("Failed to fetch LinkedIn profile", e);
        }
    }

    /**
     * Import LinkedIn profile data to user profile
     */
    public User importLinkedInProfile(String userId, String accessToken) {
        if (!enabled) {
            throw new UnsupportedOperationException("LinkedIn integration is disabled");
        }

        try {
            LinkedInProfile linkedInProfile = getProfile(accessToken);
            
            User user = userRepository.findById(userId)
                    .orElseThrow(() -> new RuntimeException("User not found: " + userId));
            
            // Update user profile with LinkedIn data
            updateUserFromLinkedInProfile(user, linkedInProfile);
            
            // Store LinkedIn connection info
            user.getIntegrations().setLinkedInConnected(true);
            user.getIntegrations().setLinkedInProfileUrl(linkedInProfile.getPublicProfileUrl());
            
            User savedUser = userRepository.save(user);
            logger.info("Successfully imported LinkedIn profile for user: {}", userId);
            
            return savedUser;
            
        } catch (Exception e) {
            logger.error("Error importing LinkedIn profile for user: {}", userId, e);
            throw new RuntimeException("Failed to import LinkedIn profile", e);
        }
    }

    /**
     * Get LinkedIn connections (if available)
     */
    public List<LinkedInConnection> getConnections(String accessToken) {
        if (!enabled) {
            return Collections.emptyList();
        }

        try {
            String connectionsUrl = baseUrl + "/people/~/connections:(id,firstName,lastName,headline,industry,location,pictureUrl)";
            JsonNode connectionsData = makeLinkedInApiCall(connectionsUrl, accessToken);
            
            return parseLinkedInConnections(connectionsData);
            
        } catch (Exception e) {
            logger.error("Error fetching LinkedIn connections", e);
            return Collections.emptyList();
        }
    }

    /**
     * Post update to LinkedIn (if user has granted permission)
     */
    public boolean postUpdate(String accessToken, String message) {
        if (!enabled) {
            return false;
        }

        try {
            String postUrl = baseUrl + "/people/~/shares";
            
            Map<String, Object> shareData = new HashMap<>();
            shareData.put("comment", message);
            shareData.put("visibility", Map.of("code", "anyone"));
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(accessToken);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(shareData, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(postUrl, request, String.class);
            
            return response.getStatusCode().is2xxSuccessful();
            
        } catch (Exception e) {
            logger.error("Error posting update to LinkedIn", e);
            return false;
        }
    }

    /**
     * Search for people on LinkedIn
     */
    public List<LinkedInProfile> searchPeople(String accessToken, String keywords, String industry) {
        if (!enabled) {
            return Collections.emptyList();
        }

        try {
            String searchUrl = baseUrl + "/people-search:(people:(id,firstName,lastName,headline,industry,location,pictureUrl))";
            
            if (keywords != null && !keywords.isEmpty()) {
                searchUrl += "?keywords=" + keywords;
            }
            
            if (industry != null && !industry.isEmpty()) {
                searchUrl += (searchUrl.contains("?") ? "&" : "?") + "facet=industry," + industry;
            }
            
            JsonNode searchData = makeLinkedInApiCall(searchUrl, accessToken);
            return parseLinkedInSearchResults(searchData);
            
        } catch (Exception e) {
            logger.error("Error searching LinkedIn people", e);
            return Collections.emptyList();
        }
    }

    // Private helper methods
    
    private JsonNode makeLinkedInApiCall(String url, String accessToken) throws Exception {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
        
        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, request, String.class);
        
        if (response.getStatusCode().is2xxSuccessful()) {
            return objectMapper.readTree(response.getBody());
        } else {
            throw new RuntimeException("LinkedIn API call failed: " + response.getStatusCode());
        }
    }

    private LinkedInProfile parseLinkedInProfile(JsonNode profileData, JsonNode emailData) {
        LinkedInProfile profile = new LinkedInProfile();
        
        profile.setId(profileData.path("id").asText());
        profile.setFirstName(profileData.path("firstName").asText());
        profile.setLastName(profileData.path("lastName").asText());
        profile.setHeadline(profileData.path("headline").asText());
        profile.setSummary(profileData.path("summary").asText());
        profile.setIndustry(profileData.path("industry").asText());
        profile.setLocation(profileData.path("location").path("name").asText());
        profile.setPictureUrl(profileData.path("pictureUrl").asText());
        profile.setPublicProfileUrl(profileData.path("publicProfileUrl").asText());
        
        // Extract email from email data
        JsonNode emailElements = emailData.path("elements");
        if (emailElements.isArray() && emailElements.size() > 0) {
            String email = emailElements.get(0).path("handle~").path("emailAddress").asText();
            profile.setEmail(email);
        }
        
        return profile;
    }

    private List<LinkedInConnection> parseLinkedInConnections(JsonNode connectionsData) {
        List<LinkedInConnection> connections = new ArrayList<>();
        
        JsonNode values = connectionsData.path("values");
        if (values.isArray()) {
            for (JsonNode connectionNode : values) {
                LinkedInConnection connection = new LinkedInConnection();
                connection.setId(connectionNode.path("id").asText());
                connection.setFirstName(connectionNode.path("firstName").asText());
                connection.setLastName(connectionNode.path("lastName").asText());
                connection.setHeadline(connectionNode.path("headline").asText());
                connection.setIndustry(connectionNode.path("industry").asText());
                connection.setLocation(connectionNode.path("location").path("name").asText());
                connection.setPictureUrl(connectionNode.path("pictureUrl").asText());
                
                connections.add(connection);
            }
        }
        
        return connections;
    }

    private List<LinkedInProfile> parseLinkedInSearchResults(JsonNode searchData) {
        List<LinkedInProfile> results = new ArrayList<>();
        
        JsonNode people = searchData.path("people").path("values");
        if (people.isArray()) {
            for (JsonNode personNode : people) {
                LinkedInProfile profile = new LinkedInProfile();
                profile.setId(personNode.path("id").asText());
                profile.setFirstName(personNode.path("firstName").asText());
                profile.setLastName(personNode.path("lastName").asText());
                profile.setHeadline(personNode.path("headline").asText());
                profile.setIndustry(personNode.path("industry").asText());
                profile.setLocation(personNode.path("location").path("name").asText());
                profile.setPictureUrl(personNode.path("pictureUrl").asText());
                
                results.add(profile);
            }
        }
        
        return results;
    }

    private void updateUserFromLinkedInProfile(User user, LinkedInProfile linkedInProfile) {
        // Update basic profile information
        if (user.getFirstName() == null || user.getFirstName().isEmpty()) {
            user.setFirstName(linkedInProfile.getFirstName());
        }
        
        if (user.getLastName() == null || user.getLastName().isEmpty()) {
            user.setLastName(linkedInProfile.getLastName());
        }
        
        if (user.getEmail() == null || user.getEmail().isEmpty()) {
            user.setEmail(linkedInProfile.getEmail());
        }
        
        // Update professional information
        User.ProfessionalInfo professionalInfo = user.getProfessionalInfo();
        if (professionalInfo == null) {
            professionalInfo = new User.ProfessionalInfo();
            user.setProfessionalInfo(professionalInfo);
        }
        
        if (professionalInfo.getCurrentTitle() == null || professionalInfo.getCurrentTitle().isEmpty()) {
            professionalInfo.setCurrentTitle(linkedInProfile.getHeadline());
        }
        
        if (professionalInfo.getIndustry() == null || professionalInfo.getIndustry().isEmpty()) {
            professionalInfo.setIndustry(linkedInProfile.getIndustry());
        }
        
        if (professionalInfo.getSummary() == null || professionalInfo.getSummary().isEmpty()) {
            professionalInfo.setSummary(linkedInProfile.getSummary());
        }
        
        // Update location
        if (user.getLocation() == null || user.getLocation().isEmpty()) {
            user.setLocation(linkedInProfile.getLocation());
        }
        
        // Update profile picture URL
        if (user.getProfilePictureUrl() == null || user.getProfilePictureUrl().isEmpty()) {
            user.setProfilePictureUrl(linkedInProfile.getPictureUrl());
        }
    }

    // Response DTOs
    
    public static class LinkedInTokenResponse {
        private String accessToken;
        private String tokenType;
        private Integer expiresIn;
        private String scope;

        // Getters and setters
        public String getAccessToken() { return accessToken; }
        public void setAccessToken(String accessToken) { this.accessToken = accessToken; }
        public String getTokenType() { return tokenType; }
        public void setTokenType(String tokenType) { this.tokenType = tokenType; }
        public Integer getExpiresIn() { return expiresIn; }
        public void setExpiresIn(Integer expiresIn) { this.expiresIn = expiresIn; }
        public String getScope() { return scope; }
        public void setScope(String scope) { this.scope = scope; }
    }

    public static class LinkedInProfile {
        private String id;
        private String firstName;
        private String lastName;
        private String email;
        private String headline;
        private String summary;
        private String industry;
        private String location;
        private String pictureUrl;
        private String publicProfileUrl;

        // Getters and setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getFirstName() { return firstName; }
        public void setFirstName(String firstName) { this.firstName = firstName; }
        public String getLastName() { return lastName; }
        public void setLastName(String lastName) { this.lastName = lastName; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getHeadline() { return headline; }
        public void setHeadline(String headline) { this.headline = headline; }
        public String getSummary() { return summary; }
        public void setSummary(String summary) { this.summary = summary; }
        public String getIndustry() { return industry; }
        public void setIndustry(String industry) { this.industry = industry; }
        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }
        public String getPictureUrl() { return pictureUrl; }
        public void setPictureUrl(String pictureUrl) { this.pictureUrl = pictureUrl; }
        public String getPublicProfileUrl() { return publicProfileUrl; }
        public void setPublicProfileUrl(String publicProfileUrl) { this.publicProfileUrl = publicProfileUrl; }
    }

    public static class LinkedInConnection {
        private String id;
        private String firstName;
        private String lastName;
        private String headline;
        private String industry;
        private String location;
        private String pictureUrl;

        // Getters and setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getFirstName() { return firstName; }
        public void setFirstName(String firstName) { this.firstName = firstName; }
        public String getLastName() { return lastName; }
        public void setLastName(String lastName) { this.lastName = lastName; }
        public String getHeadline() { return headline; }
        public void setHeadline(String headline) { this.headline = headline; }
        public String getIndustry() { return industry; }
        public void setIndustry(String industry) { this.industry = industry; }
        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }
        public String getPictureUrl() { return pictureUrl; }
        public void setPictureUrl(String pictureUrl) { this.pictureUrl = pictureUrl; }
    }
}
