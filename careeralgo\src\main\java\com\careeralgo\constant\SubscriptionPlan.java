package com.careeralgo.constant;

/**
 * Subscription plans available in the CareerAlgo system
 */
public enum SubscriptionPlan {
    FREE("FREE"),
    PRO("PRO"),
    ENTERPRISE("ENTERPRISE");

    private final String value;

    SubscriptionPlan(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
