package com.careeralgo.controller;

import com.careeralgo.model.User;
import com.careeralgo.service.LinkedInApiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * REST controller for LinkedIn integration
 */
@RestController
@RequestMapping("/integrations/linkedin")
@Tag(name = "LinkedIn Integration", description = "APIs for LinkedIn profile integration and networking")
public class LinkedInController {

    @Autowired
    private LinkedInApiService linkedInApiService;

    /**
     * Get LinkedIn authorization URL
     */
    @GetMapping("/auth-url")
    @Operation(summary = "Get LinkedIn authorization URL", description = "Generate LinkedIn OAuth authorization URL for user authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Authorization URL generated successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "503", description = "LinkedIn integration disabled")
    })
    public ResponseEntity<Map<String, String>> getAuthorizationUrl(Authentication authentication) {
        try {
            // Generate a unique state parameter for security
            String state = UUID.randomUUID().toString();
            
            // TODO: Store state in session or cache for validation
            
            String authUrl = linkedInApiService.generateAuthorizationUrl(state);
            
            return ResponseEntity.ok(Map.of(
                    "authorizationUrl", authUrl,
                    "state", state,
                    "message", "Redirect user to this URL to authorize LinkedIn access"
            ));
            
        } catch (UnsupportedOperationException e) {
            return ResponseEntity.status(503).body(Map.of(
                    "error", "LinkedIn integration is currently disabled",
                    "message", e.getMessage()
            ));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of(
                    "error", "Failed to generate authorization URL",
                    "message", e.getMessage()
            ));
        }
    }

    /**
     * Handle LinkedIn OAuth callback
     */
    @PostMapping("/callback")
    @Operation(summary = "Handle LinkedIn OAuth callback", description = "Process LinkedIn OAuth callback and exchange code for access token")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "LinkedIn integration completed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid authorization code or state"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "503", description = "LinkedIn integration disabled")
    })
    public ResponseEntity<Map<String, Object>> handleCallback(
            Authentication authentication,
            @RequestBody LinkedInCallbackRequest request) {
        
        try {
            // TODO: Validate state parameter
            
            // Exchange authorization code for access token
            LinkedInApiService.LinkedInTokenResponse tokenResponse = 
                    linkedInApiService.exchangeCodeForToken(request.getCode());
            
            // Get user ID from authentication
            String userId = getUserId(authentication);
            
            // Import LinkedIn profile data
            User updatedUser = linkedInApiService.importLinkedInProfile(userId, tokenResponse.getAccessToken());
            
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "LinkedIn profile imported successfully",
                    "user", Map.of(
                            "id", updatedUser.getId(),
                            "firstName", updatedUser.getFirstName(),
                            "lastName", updatedUser.getLastName(),
                            "linkedInConnected", updatedUser.getIntegrations().isLinkedInConnected()
                    )
            ));
            
        } catch (UnsupportedOperationException e) {
            return ResponseEntity.status(503).body(Map.of(
                    "error", "LinkedIn integration is currently disabled",
                    "message", e.getMessage()
            ));
        } catch (Exception e) {
            return ResponseEntity.status(400).body(Map.of(
                    "error", "Failed to process LinkedIn callback",
                    "message", e.getMessage()
            ));
        }
    }

    /**
     * Get LinkedIn profile
     */
    @PostMapping("/profile")
    @Operation(summary = "Get LinkedIn profile", description = "Fetch LinkedIn profile information using access token")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "LinkedIn profile retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid access token"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "503", description = "LinkedIn integration disabled")
    })
    public ResponseEntity<LinkedInApiService.LinkedInProfile> getLinkedInProfile(
            @RequestBody Map<String, String> request) {
        
        try {
            String accessToken = request.get("accessToken");
            if (accessToken == null || accessToken.isEmpty()) {
                return ResponseEntity.badRequest().build();
            }
            
            LinkedInApiService.LinkedInProfile profile = linkedInApiService.getProfile(accessToken);
            return ResponseEntity.ok(profile);
            
        } catch (UnsupportedOperationException e) {
            return ResponseEntity.status(503).build();
        } catch (Exception e) {
            return ResponseEntity.status(400).build();
        }
    }

    /**
     * Import LinkedIn profile
     */
    @PostMapping("/import")
    @Operation(summary = "Import LinkedIn profile", description = "Import LinkedIn profile data to user account")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Profile imported successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid access token"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "503", description = "LinkedIn integration disabled")
    })
    public ResponseEntity<Map<String, Object>> importProfile(
            Authentication authentication,
            @RequestBody Map<String, String> request) {
        
        try {
            String accessToken = request.get("accessToken");
            if (accessToken == null || accessToken.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of(
                        "error", "Access token is required"
                ));
            }
            
            String userId = getUserId(authentication);
            User updatedUser = linkedInApiService.importLinkedInProfile(userId, accessToken);
            
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "LinkedIn profile imported successfully",
                    "user", Map.of(
                            "id", updatedUser.getId(),
                            "firstName", updatedUser.getFirstName(),
                            "lastName", updatedUser.getLastName(),
                            "email", updatedUser.getEmail(),
                            "linkedInConnected", updatedUser.getIntegrations().isLinkedInConnected()
                    )
            ));
            
        } catch (UnsupportedOperationException e) {
            return ResponseEntity.status(503).body(Map.of(
                    "error", "LinkedIn integration is currently disabled"
            ));
        } catch (Exception e) {
            return ResponseEntity.status(400).body(Map.of(
                    "error", "Failed to import LinkedIn profile",
                    "message", e.getMessage()
            ));
        }
    }

    /**
     * Get LinkedIn connections
     */
    @PostMapping("/connections")
    @Operation(summary = "Get LinkedIn connections", description = "Fetch user's LinkedIn connections")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Connections retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid access token"),
            @ApiResponse(responseCode = "503", description = "LinkedIn integration disabled")
    })
    public ResponseEntity<List<LinkedInApiService.LinkedInConnection>> getConnections(
            @RequestBody Map<String, String> request) {
        
        try {
            String accessToken = request.get("accessToken");
            if (accessToken == null || accessToken.isEmpty()) {
                return ResponseEntity.badRequest().build();
            }
            
            List<LinkedInApiService.LinkedInConnection> connections = 
                    linkedInApiService.getConnections(accessToken);
            
            return ResponseEntity.ok(connections);
            
        } catch (UnsupportedOperationException e) {
            return ResponseEntity.status(503).build();
        } catch (Exception e) {
            return ResponseEntity.status(400).build();
        }
    }

    /**
     * Post update to LinkedIn
     */
    @PostMapping("/post")
    @Operation(summary = "Post to LinkedIn", description = "Post an update to user's LinkedIn profile")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Post shared successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "503", description = "LinkedIn integration disabled")
    })
    public ResponseEntity<Map<String, Object>> postUpdate(
            @RequestBody LinkedInPostRequest request) {
        
        try {
            if (request.getAccessToken() == null || request.getMessage() == null) {
                return ResponseEntity.badRequest().body(Map.of(
                        "error", "Access token and message are required"
                ));
            }
            
            boolean success = linkedInApiService.postUpdate(request.getAccessToken(), request.getMessage());
            
            if (success) {
                return ResponseEntity.ok(Map.of(
                        "success", true,
                        "message", "Update posted to LinkedIn successfully"
                ));
            } else {
                return ResponseEntity.status(400).body(Map.of(
                        "error", "Failed to post update to LinkedIn"
                ));
            }
            
        } catch (UnsupportedOperationException e) {
            return ResponseEntity.status(503).body(Map.of(
                    "error", "LinkedIn integration is currently disabled"
            ));
        } catch (Exception e) {
            return ResponseEntity.status(400).body(Map.of(
                    "error", "Failed to post update",
                    "message", e.getMessage()
            ));
        }
    }

    /**
     * Search LinkedIn people
     */
    @PostMapping("/search")
    @Operation(summary = "Search LinkedIn people", description = "Search for people on LinkedIn")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Search completed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid search parameters"),
            @ApiResponse(responseCode = "503", description = "LinkedIn integration disabled")
    })
    public ResponseEntity<List<LinkedInApiService.LinkedInProfile>> searchPeople(
            @RequestBody LinkedInSearchRequest request) {
        
        try {
            if (request.getAccessToken() == null) {
                return ResponseEntity.badRequest().build();
            }
            
            List<LinkedInApiService.LinkedInProfile> results = linkedInApiService.searchPeople(
                    request.getAccessToken(), 
                    request.getKeywords(), 
                    request.getIndustry()
            );
            
            return ResponseEntity.ok(results);
            
        } catch (UnsupportedOperationException e) {
            return ResponseEntity.status(503).build();
        } catch (Exception e) {
            return ResponseEntity.status(400).build();
        }
    }

    /**
     * Disconnect LinkedIn integration
     */
    @DeleteMapping("/disconnect")
    @Operation(summary = "Disconnect LinkedIn", description = "Disconnect LinkedIn integration from user account")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "LinkedIn disconnected successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Map<String, String>> disconnectLinkedIn(Authentication authentication) {
        try {
            // TODO: Implement LinkedIn disconnection
            // This would involve removing stored tokens and updating user integration status
            
            return ResponseEntity.ok(Map.of(
                    "success", "true",
                    "message", "LinkedIn integration disconnected successfully"
            ));
            
        } catch (Exception e) {
            return ResponseEntity.status(400).body(Map.of(
                    "error", "Failed to disconnect LinkedIn integration",
                    "message", e.getMessage()
            ));
        }
    }

    /**
     * Get integration status
     */
    @GetMapping("/status")
    @Operation(summary = "Get LinkedIn integration status", description = "Check if LinkedIn is connected for the user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Status retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<Map<String, Object>> getIntegrationStatus(Authentication authentication) {
        try {
            // TODO: Get actual integration status from user profile
            
            return ResponseEntity.ok(Map.of(
                    "linkedInConnected", false,
                    "linkedInProfileUrl", "",
                    "lastSyncDate", "",
                    "features", Map.of(
                            "profileImport", true,
                            "connectionSync", true,
                            "posting", true,
                            "jobSearch", false
                    )
            ));
            
        } catch (Exception e) {
            return ResponseEntity.status(400).body(Map.of(
                    "error", "Failed to get integration status",
                    "message", e.getMessage()
            ));
        }
    }

    // Helper methods
    
    private String getUserId(Authentication authentication) {
        if (authentication.getPrincipal() instanceof Jwt jwt) {
            return jwt.getSubject(); // This would need to be mapped to internal user ID
        }
        throw new IllegalArgumentException("Invalid authentication type");
    }

    // Request DTOs
    
    public static class LinkedInCallbackRequest {
        private String code;
        private String state;

        // Getters and setters
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getState() { return state; }
        public void setState(String state) { this.state = state; }
    }

    public static class LinkedInPostRequest {
        private String accessToken;
        private String message;

        // Getters and setters
        public String getAccessToken() { return accessToken; }
        public void setAccessToken(String accessToken) { this.accessToken = accessToken; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    public static class LinkedInSearchRequest {
        private String accessToken;
        private String keywords;
        private String industry;

        // Getters and setters
        public String getAccessToken() { return accessToken; }
        public void setAccessToken(String accessToken) { this.accessToken = accessToken; }
        public String getKeywords() { return keywords; }
        public void setKeywords(String keywords) { this.keywords = keywords; }
        public String getIndustry() { return industry; }
        public void setIndustry(String industry) { this.industry = industry; }
    }
}
