package com.careeralgo.constant;

/**
 * Experience levels for users and jobs
 */
public enum ExperienceLevel {
    ENTRY("ENTRY"),
    MID_LEVEL("MID_LEVEL"),
    SENIOR("SENIOR"),
    EXECUTIVE("EXECUTIVE");

    private final String value;

    ExperienceLevel(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
