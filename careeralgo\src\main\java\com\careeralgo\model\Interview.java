package com.careeralgo.model;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Interview information for job applications
 */
public class Interview {
    
    private InterviewType type;
    private Integer round;
    private LocalDateTime scheduledDate;
    private Integer duration; // in minutes
    private Interviewer interviewer;
    private String meetingLink;
    private String preparationNotes;
    private InterviewFeedback feedback;
    private List<String> questions;

    public enum InterviewType {
        PHONE, VIDEO, ONSITE, TECHNICAL
    }

    // Constructors
    public Interview() {}

    public Interview(InterviewType type, Integer round, LocalDateTime scheduledDate) {
        this.type = type;
        this.round = round;
        this.scheduledDate = scheduledDate;
    }

    // Getters and Setters
    public InterviewType getType() {
        return type;
    }

    public void setType(InterviewType type) {
        this.type = type;
    }

    public Integer getRound() {
        return round;
    }

    public void setRound(Integer round) {
        this.round = round;
    }

    public LocalDateTime getScheduledDate() {
        return scheduledDate;
    }

    public void setScheduledDate(LocalDateTime scheduledDate) {
        this.scheduledDate = scheduledDate;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Interviewer getInterviewer() {
        return interviewer;
    }

    public void setInterviewer(Interviewer interviewer) {
        this.interviewer = interviewer;
    }

    public String getMeetingLink() {
        return meetingLink;
    }

    public void setMeetingLink(String meetingLink) {
        this.meetingLink = meetingLink;
    }

    public String getPreparationNotes() {
        return preparationNotes;
    }

    public void setPreparationNotes(String preparationNotes) {
        this.preparationNotes = preparationNotes;
    }

    public InterviewFeedback getFeedback() {
        return feedback;
    }

    public void setFeedback(InterviewFeedback feedback) {
        this.feedback = feedback;
    }

    public List<String> getQuestions() {
        return questions;
    }

    public void setQuestions(List<String> questions) {
        this.questions = questions;
    }

    public boolean isUpcoming() {
        return scheduledDate != null && scheduledDate.isAfter(LocalDateTime.now());
    }

    public boolean isPast() {
        return scheduledDate != null && scheduledDate.isBefore(LocalDateTime.now());
    }

    public String getDisplayTitle() {
        return "Round " + round + " - " + type.toString().replace("_", " ");
    }

    /**
     * Interviewer information
     */
    public static class Interviewer {
        private String name;
        private String title;
        private String email;

        // Constructors
        public Interviewer() {}

        public Interviewer(String name, String title, String email) {
            this.name = name;
            this.title = title;
            this.email = email;
        }

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getDisplayName() {
            return name + (title != null ? " (" + title + ")" : "");
        }
    }

    /**
     * Interview feedback
     */
    public static class InterviewFeedback {
        private Integer rating; // 1-5 scale
        private String notes;
        private String nextSteps;

        // Constructors
        public InterviewFeedback() {}

        public InterviewFeedback(Integer rating, String notes, String nextSteps) {
            this.rating = rating;
            this.notes = notes;
            this.nextSteps = nextSteps;
        }

        // Getters and Setters
        public Integer getRating() {
            return rating;
        }

        public void setRating(Integer rating) {
            this.rating = rating;
        }

        public String getNotes() {
            return notes;
        }

        public void setNotes(String notes) {
            this.notes = notes;
        }

        public String getNextSteps() {
            return nextSteps;
        }

        public void setNextSteps(String nextSteps) {
            this.nextSteps = nextSteps;
        }

        public String getRatingDescription() {
            if (rating == null) return "Not rated";
            switch (rating) {
                case 1: return "Poor";
                case 2: return "Below Average";
                case 3: return "Average";
                case 4: return "Good";
                case 5: return "Excellent";
                default: return "Unknown";
            }
        }
    }
}
