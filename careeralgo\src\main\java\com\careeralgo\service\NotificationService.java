package com.careeralgo.service;

import com.careeralgo.exception.ResourceNotFoundException;
import com.careeralgo.model.Notification;
import com.careeralgo.model.User;
import com.careeralgo.repository.NotificationRepository;
import com.careeralgo.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for notification management
 */
@Service
public class NotificationService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationService.class);

    @Autowired
    private NotificationRepository notificationRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RealTimeNotificationService realTimeNotificationService;

    /**
     * Get user's notifications with pagination
     */
    public Page<Notification> getUserNotifications(Authentication authentication, int page, int size, 
                                                 Boolean unreadOnly, Notification.NotificationType type) {
        String userId = getUserId(authentication);
        Pageable pageable = PageRequest.of(page, size);
        
        if (unreadOnly != null && unreadOnly && type != null) {
            // Filter by unread and type - need custom query
            return notificationRepository.findByUserIdAndTypeInAndIsRead(
                    userId, List.of(type), false, pageable);
        } else if (unreadOnly != null && unreadOnly) {
            return notificationRepository.findByUserIdAndIsReadFalseOrderByCreatedAtDesc(userId, pageable);
        } else if (type != null) {
            return notificationRepository.findByUserIdAndTypeOrderByCreatedAtDesc(userId, type, pageable);
        } else {
            return notificationRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
        }
    }

    /**
     * Get notification by ID
     */
    public Notification getNotificationById(Authentication authentication, String notificationId) {
        String userId = getUserId(authentication);
        Notification notification = notificationRepository.findById(notificationId)
                .orElseThrow(() -> new ResourceNotFoundException("Notification not found: " + notificationId));
        
        if (!notification.getUserId().equals(userId)) {
            throw new ResourceNotFoundException("Notification not found: " + notificationId);
        }
        
        return notification;
    }

    /**
     * Create and send notification
     */
    public Notification createNotification(String userId, Notification.NotificationType type, 
                                         String title, String message, String actionUrl, 
                                         Map<String, Object> data) {
        Notification notification = new Notification(userId, type, title, message);
        notification.setActionUrl(actionUrl);
        notification.setData(data);
        
        // Set priority based on type
        notification.setPriority(getPriorityForType(type));
        
        // Set expiration (default 30 days)
        notification.setExpiresAt(LocalDateTime.now().plusDays(30));
        
        Notification savedNotification = notificationRepository.save(notification);
        
        // Send real-time notification if user is online
        realTimeNotificationService.sendNotificationToUser(userId, savedNotification);
        
        logger.info("Created notification for user: {} of type: {}", userId, type);
        return savedNotification;
    }

    /**
     * Create scheduled notification
     */
    public Notification createScheduledNotification(String userId, Notification.NotificationType type,
                                                  String title, String message, LocalDateTime scheduledAt,
                                                  String actionUrl, Map<String, Object> data) {
        Notification notification = new Notification(userId, type, title, message);
        notification.setActionUrl(actionUrl);
        notification.setData(data);
        notification.setScheduledAt(scheduledAt);
        notification.setPriority(getPriorityForType(type));
        notification.setExpiresAt(scheduledAt.plusDays(7)); // Expire 7 days after scheduled time
        
        Notification savedNotification = notificationRepository.save(notification);
        logger.info("Created scheduled notification for user: {} at: {}", userId, scheduledAt);
        
        return savedNotification;
    }

    /**
     * Mark notification as read
     */
    public Notification markAsRead(Authentication authentication, String notificationId) {
        String userId = getUserId(authentication);
        Notification notification = getNotificationById(authentication, notificationId);
        
        if (!notification.isRead()) {
            notification.markAsRead();
            notification = notificationRepository.save(notification);
            logger.info("Marked notification as read: {} for user: {}", notificationId, userId);
        }
        
        return notification;
    }

    /**
     * Mark multiple notifications as read
     */
    public void markMultipleAsRead(Authentication authentication, List<String> notificationIds) {
        String userId = getUserId(authentication);
        
        List<Notification> notifications = notificationRepository.findByIdIn(notificationIds);
        
        for (Notification notification : notifications) {
            if (notification.getUserId().equals(userId) && !notification.isRead()) {
                notification.markAsRead();
            }
        }
        
        notificationRepository.saveAll(notifications);
        logger.info("Marked {} notifications as read for user: {}", notifications.size(), userId);
    }

    /**
     * Mark all notifications as read
     */
    public void markAllAsRead(Authentication authentication) {
        String userId = getUserId(authentication);
        
        List<Notification> unreadNotifications = notificationRepository.findByUserIdAndIsReadFalseOrderByCreatedAtDesc(userId);
        
        for (Notification notification : unreadNotifications) {
            notification.markAsRead();
        }
        
        notificationRepository.saveAll(unreadNotifications);
        logger.info("Marked all {} notifications as read for user: {}", unreadNotifications.size(), userId);
    }

    /**
     * Delete notification
     */
    public void deleteNotification(Authentication authentication, String notificationId) {
        String userId = getUserId(authentication);
        Notification notification = getNotificationById(authentication, notificationId);
        
        notificationRepository.delete(notification);
        logger.info("Deleted notification: {} for user: {}", notificationId, userId);
    }

    /**
     * Delete multiple notifications
     */
    public void deleteMultipleNotifications(Authentication authentication, List<String> notificationIds) {
        String userId = getUserId(authentication);
        
        List<Notification> notifications = notificationRepository.findByIdIn(notificationIds);
        List<Notification> userNotifications = notifications.stream()
                .filter(n -> n.getUserId().equals(userId))
                .toList();
        
        notificationRepository.deleteAll(userNotifications);
        logger.info("Deleted {} notifications for user: {}", userNotifications.size(), userId);
    }

    /**
     * Get notification statistics
     */
    public NotificationStats getNotificationStats(Authentication authentication) {
        String userId = getUserId(authentication);
        
        NotificationStats stats = new NotificationStats();
        stats.setUserId(userId);
        stats.setGeneratedAt(LocalDateTime.now());
        
        // Count total and unread notifications
        stats.setTotalNotifications(notificationRepository.findByUserIdOrderByCreatedAtDesc(userId).size());
        stats.setUnreadCount(notificationRepository.countByUserIdAndIsReadFalse(userId));
        
        // Count by type
        Map<String, Long> notificationsByType = new HashMap<>();
        for (Notification.NotificationType type : Notification.NotificationType.values()) {
            long count = notificationRepository.countByUserIdAndType(userId, type);
            notificationsByType.put(type.name(), count);
        }
        stats.setNotificationsByType(notificationsByType);
        
        // Recent notifications (last 24 hours)
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        stats.setRecentNotifications(notificationRepository.findRecentNotifications(userId, yesterday).size());
        
        // High priority unread
        stats.setHighPriorityUnread(notificationRepository.findHighPriorityUnreadNotifications(userId).size());
        
        return stats;
    }

    /**
     * Search notifications
     */
    public Page<Notification> searchNotifications(Authentication authentication, String searchTerm, 
                                                int page, int size) {
        String userId = getUserId(authentication);
        Pageable pageable = PageRequest.of(page, size);
        
        return notificationRepository.searchNotifications(userId, searchTerm, pageable);
    }

    /**
     * Get notification preferences
     */
    public NotificationPreferences getNotificationPreferences(Authentication authentication) {
        String userId = getUserId(authentication);
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Convert user preferences to notification preferences
        NotificationPreferences preferences = new NotificationPreferences();
        preferences.setUserId(userId);
        preferences.setEmailNotifications(user.getPreferences().isEmailNotifications());
        preferences.setPushNotifications(user.getPreferences().isPushNotifications());
        preferences.setJobAlerts(user.getPreferences().isJobAlerts());
        preferences.setWeeklyReports(user.getPreferences().isWeeklyReports());
        
        // Default real-time notification settings
        preferences.setRealTimeNotifications(true);
        preferences.setJobMatchNotifications(true);
        preferences.setApplicationUpdates(true);
        preferences.setInterviewReminders(true);
        preferences.setOfferNotifications(true);
        
        return preferences;
    }

    /**
     * Update notification preferences
     */
    public NotificationPreferences updateNotificationPreferences(Authentication authentication, 
                                                               NotificationPreferences preferences) {
        String userId = getUserId(authentication);
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Update user preferences
        User.UserPreferences userPrefs = user.getPreferences();
        userPrefs.setEmailNotifications(preferences.isEmailNotifications());
        userPrefs.setPushNotifications(preferences.isPushNotifications());
        userPrefs.setJobAlerts(preferences.isJobAlerts());
        userPrefs.setWeeklyReports(preferences.isWeeklyReports());
        
        userRepository.save(user);
        
        logger.info("Updated notification preferences for user: {}", userId);
        return preferences;
    }

    // Helper methods
    
    private String getUserId(Authentication authentication) {
        if (authentication.getPrincipal() instanceof Jwt jwt) {
            String clerkUserId = jwt.getSubject();
            User user = userRepository.findByClerkUserId(clerkUserId)
                    .orElseThrow(() -> new ResourceNotFoundException("User not found"));
            return user.getId();
        }
        throw new IllegalArgumentException("Invalid authentication type");
    }

    private Notification.NotificationPriority getPriorityForType(Notification.NotificationType type) {
        return switch (type) {
            case OFFER_RECEIVED, INTERVIEW_REMINDER -> Notification.NotificationPriority.HIGH;
            case APPLICATION_UPDATE, JOB_MATCH -> Notification.NotificationPriority.MEDIUM;
            case SYSTEM_ANNOUNCEMENT -> Notification.NotificationPriority.URGENT;
            default -> Notification.NotificationPriority.LOW;
        };
    }

    // DTOs
    
    public static class NotificationStats {
        private String userId;
        private LocalDateTime generatedAt;
        private Integer totalNotifications;
        private Long unreadCount;
        private Map<String, Long> notificationsByType;
        private Integer recentNotifications;
        private Integer highPriorityUnread;

        // Getters and setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public LocalDateTime getGeneratedAt() { return generatedAt; }
        public void setGeneratedAt(LocalDateTime generatedAt) { this.generatedAt = generatedAt; }
        public Integer getTotalNotifications() { return totalNotifications; }
        public void setTotalNotifications(Integer totalNotifications) { this.totalNotifications = totalNotifications; }
        public Long getUnreadCount() { return unreadCount; }
        public void setUnreadCount(Long unreadCount) { this.unreadCount = unreadCount; }
        public Map<String, Long> getNotificationsByType() { return notificationsByType; }
        public void setNotificationsByType(Map<String, Long> notificationsByType) { this.notificationsByType = notificationsByType; }
        public Integer getRecentNotifications() { return recentNotifications; }
        public void setRecentNotifications(Integer recentNotifications) { this.recentNotifications = recentNotifications; }
        public Integer getHighPriorityUnread() { return highPriorityUnread; }
        public void setHighPriorityUnread(Integer highPriorityUnread) { this.highPriorityUnread = highPriorityUnread; }
    }

    public static class NotificationPreferences {
        private String userId;
        private boolean emailNotifications;
        private boolean pushNotifications;
        private boolean realTimeNotifications;
        private boolean jobAlerts;
        private boolean weeklyReports;
        private boolean jobMatchNotifications;
        private boolean applicationUpdates;
        private boolean interviewReminders;
        private boolean offerNotifications;

        // Getters and setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public boolean isEmailNotifications() { return emailNotifications; }
        public void setEmailNotifications(boolean emailNotifications) { this.emailNotifications = emailNotifications; }
        public boolean isPushNotifications() { return pushNotifications; }
        public void setPushNotifications(boolean pushNotifications) { this.pushNotifications = pushNotifications; }
        public boolean isRealTimeNotifications() { return realTimeNotifications; }
        public void setRealTimeNotifications(boolean realTimeNotifications) { this.realTimeNotifications = realTimeNotifications; }
        public boolean isJobAlerts() { return jobAlerts; }
        public void setJobAlerts(boolean jobAlerts) { this.jobAlerts = jobAlerts; }
        public boolean isWeeklyReports() { return weeklyReports; }
        public void setWeeklyReports(boolean weeklyReports) { this.weeklyReports = weeklyReports; }
        public boolean isJobMatchNotifications() { return jobMatchNotifications; }
        public void setJobMatchNotifications(boolean jobMatchNotifications) { this.jobMatchNotifications = jobMatchNotifications; }
        public boolean isApplicationUpdates() { return applicationUpdates; }
        public void setApplicationUpdates(boolean applicationUpdates) { this.applicationUpdates = applicationUpdates; }
        public boolean isInterviewReminders() { return interviewReminders; }
        public void setInterviewReminders(boolean interviewReminders) { this.interviewReminders = interviewReminders; }
        public boolean isOfferNotifications() { return offerNotifications; }
        public void setOfferNotifications(boolean offerNotifications) { this.offerNotifications = offerNotifications; }
    }
}
