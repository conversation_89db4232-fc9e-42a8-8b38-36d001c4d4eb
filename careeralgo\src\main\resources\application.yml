# CareerAlgo Backend Configuration
spring:
  application:
    name: careeralgo-backend
  
  # MongoDB Configuration
  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://localhost:27017/careeralgo}
      auto-index-creation: true
  
  # Redis Configuration
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
  
  # Cache Configuration
  cache:
    type: redis
    redis:
      time-to-live: 600000 # 10 minutes
  
  # Security Configuration
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${CLERK_ISSUER_URI:https://your-clerk-domain.clerk.accounts.dev}
  
  # File Upload Configuration
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  
  # Jackson Configuration
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# Server Configuration
server:
  port: ${PORT:8080}
  servlet:
    context-path: /api/v1
  error:
    include-message: always
    include-binding-errors: always

# Management & Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# Logging Configuration
logging:
  level:
    com.careeralgo: ${LOG_LEVEL:INFO}
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Application Specific Configuration
careeralgo:
  # Clerk Configuration
  clerk:
    webhook-secret: ${CLERK_WEBHOOK_SECRET:your-webhook-secret}
    public-key: ${CLERK_PUBLIC_KEY:your-public-key}
    
  # AI Configuration
  ai:
    openai:
      api-key: ${OPENAI_API_KEY:your-openai-api-key}
      model: ${OPENAI_MODEL:gpt-4}
      max-tokens: ${OPENAI_MAX_TOKENS:2000}
      temperature: ${OPENAI_TEMPERATURE:0.7}
    
  # External APIs
  external:
    themuse:
      api-key: ${THEMUSE_API_KEY:your-themuse-api-key}
      base-url: ${THEMUSE_BASE_URL:https://www.themuse.com/api/public}
    linkedin:
      client-id: ${LINKEDIN_CLIENT_ID:your-linkedin-client-id}
      client-secret: ${LINKEDIN_CLIENT_SECRET:your-linkedin-client-secret}
  
  # File Storage
  storage:
    cloudinary:
      cloud-name: ${CLOUDINARY_CLOUD_NAME:your-cloud-name}
      api-key: ${CLOUDINARY_API_KEY:your-api-key}
      api-secret: ${CLOUDINARY_API_SECRET:your-api-secret}
      secure: true
  
  # Email Configuration
  email:
    sendgrid:
      api-key: ${SENDGRID_API_KEY:your-sendgrid-api-key}
      from-email: ${FROM_EMAIL:<EMAIL>}
      from-name: ${FROM_NAME:CareerAlgo}
  
  # Rate Limiting
  rate-limit:
    enabled: true
    requests-per-minute: 60
    burst-capacity: 100
  
  # CORS Configuration
  cors:
    allowed-origins: ${ALLOWED_ORIGINS:http://localhost:3000,https://careeralgo.com}
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
    max-age: 3600

# Swagger/OpenAPI Configuration
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
  info:
    title: CareerAlgo API
    description: AI-powered career development platform API
    version: 1.0.0
    contact:
      name: CareerAlgo Team
      email: <EMAIL>
